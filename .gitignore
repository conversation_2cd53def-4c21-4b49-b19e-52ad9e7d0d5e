# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST
*.pyc
*.pyo
*.pyd
.Python
env/
venv/
.env
.venv
ENV/
env.bak/
venv.bak/
pip-log.txt
pip-delete-this-directory.txt
.coverage
.pytest_cache/
htmlcov/
.tox/
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/

# Node.js / React
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.npm
.yarn-integrity
.pnp
.pnp.js
coverage/
build/
.nyc_output
.node_repl_history
*.tgz
*.tar.gz
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE / Editor
.vscode/
.idea/
*.swp
*.swo
*~
.project
.settings/
.pydevproject
*.sublime-project
*.sublime-workspace

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log
logs/
*.log*

# Database
*.db
*.sqlite
*.sqlite3

# Service Account Keys
serviceAccount.json
*.json
!package.json
!package-lock.json
!tsconfig.json

# Temporary files
*.tmp
*.temp
temp/
tmp/

# Media files for testing
*.mp3
*.mp4
*.wav
*.avi
*.mov

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# pipenv
Pipfile.lock

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Docker
.dockerignore
Dockerfile
docker-compose.yml
docker-compose.override.yml

# Misc
.cache/
.parcel-cache/
.next/
.nuxt/
.out/
.repl_history